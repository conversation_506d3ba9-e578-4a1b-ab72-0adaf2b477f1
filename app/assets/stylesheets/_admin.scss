// ABOUTME: Admin-specific styles extracted from inline styles and Bootstrap patterns
// ABOUTME: Provides consistent styling for all admin interface pages

// Admin Container
.admin-container {
  font-family: Arial, sans-serif;
  margin: 20px;
  max-width: 100%;
}

// Page Header
.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h1 {
    margin: 0;
    color: $secondary-color;
  }
}

// Cards and Sections
.admin-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 24px;
  overflow: hidden;
}

.admin-card-header {
  background-color: #f8f9fa;
  padding: 15px;
  border-bottom: 1px solid #e5e7eb;
  
  h2, h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: $secondary-color;
  }
}

.admin-card-body {
  padding: 20px;
}

// Status Summary Cards
.admin-status-summary {
  display: flex;
  gap: 20px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.admin-status-card {
  flex: 1;
  min-width: 150px;
  text-align: center;
  padding: 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  
  h3 {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: bold;
    color: $secondary-color;
  }
  
  p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
  }
  
  &.admin-status-free {
    background-color: #f8f9fa;
  }
  
  &.admin-status-premium {
    background-color: #d1fae5;
    color: $green;
    
    h3 {
      color: $green;
    }
  }
  
  &.admin-status-beta {
    background-color: $secondary-color;
    color: white;
    
    h3, p {
      color: white;
    }
  }
}

// Filter Buttons
.admin-filter-buttons {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.admin-filter-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  text-decoration: none;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  
  &:hover {
    background: #f3f4f6;
    text-decoration: none;
    color: #374151;
  }
  
  &.active {
    background: $blue;
    color: white;
    border-color: $blue;
  }
  
  &:visited {
    color: #374151;
  }
  
  &.active:visited {
    color: white;
  }
}

// Admin Tables
.admin-table-container {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: middle;
  }
  
  th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: $secondary-color;
  }
  
  tr:hover {
    background-color: #f9fafb;
  }
  
  tr.admin-table-warning {
    background-color: #fef3c7;
  }
}

// Responsive table wrapper
.admin-table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

// Flex-based Table (for complex layouts)
.admin-flex-table {
  display: flex;
  flex-direction: column;
  border: 1px solid #e5e7eb;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.admin-flex-table-header,
.admin-flex-table-row {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  align-items: stretch;
}

.admin-flex-table-header {
  background-color: #f8f9fa;
  font-weight: bold;
}

.admin-flex-table-cell {
  padding: 12px 10px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  
  &:last-child {
    border-right: none;
  }
  
  // Column widths
  &.admin-col-id {
    flex: 0 0 60px;
    white-space: nowrap;
  }
  
  &.admin-col-email {
    flex: 0 0 200px;
    white-space: nowrap;
  }
  
  &.admin-col-name {
    flex: 0 0 150px;
    white-space: nowrap;
  }
  
  &.admin-col-summary {
    flex: 1 1 auto;
    white-space: normal;
    word-break: break-word;
    overflow-wrap: break-word;
  }
  
  &.admin-col-date {
    flex: 0 0 150px;
    white-space: nowrap;
  }
  
  &.admin-col-status {
    flex: 0 0 120px;
    white-space: nowrap;
    text-align: center;
    justify-content: center;
  }
  
  &.admin-col-actions {
    flex: 0 0 320px;
    justify-content: center;
    text-align: center;
    min-width: 320px;
  }
}

.admin-flex-table-row:nth-child(even) {
  background-color: #f9fafb;
}

.admin-flex-table-row:hover {
  background-color: #f3f4f6;
}

// Status Badges
.admin-status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  display: inline-block;
  
  &.admin-status-draft {
    background-color: #fef3c7;
    color: #92400e;
  }
  
  &.admin-status-pending {
    background-color: #dbeafe;
    color: #1e40af;
  }
  
  &.admin-status-published {
    background-color: #d1fae5;
    color: #065f46;
  }
  
  &.admin-status-active {
    background-color: #d1fae5;
    color: #065f46;
  }
  
  &.admin-status-inactive {
    background-color: #f3f4f6;
    color: #6b7280;
  }
  
  &.admin-status-disabled {
    background-color: #fee2e2;
    color: #dc2626;
  }
}

// Tier Badges
.admin-tier-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  display: inline-block;
  
  &.admin-tier-free {
    background-color: #f3f4f6;
    color: #6b7280;
  }
  
  &.admin-tier-premium {
    background-color: #d1fae5;
    color: #065f46;
  }
  
  &.admin-tier-beta {
    background-color: $secondary-color;
    color: white;
  }
}

// Buttons
.admin-btn {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid transparent;
  text-align: center;
  line-height: 1.4;
  transition: all 0.2s ease;
  
  &:hover {
    text-decoration: none;
  }
}

.admin-btn-primary {
  @extend .admin-btn;
  background-color: $blue;
  color: white;
  border-color: $blue;
  
  &:hover {
    background-color: darken($blue, 10%);
    color: white;
  }
  
  &:visited {
    color: white;
  }
}

.admin-btn-secondary {
  @extend .admin-btn;
  background-color: #6c757d;
  color: white;
  border-color: #6c757d;
  
  &:hover {
    background-color: darken(#6c757d, 10%);
    color: white;
  }
  
  &:visited {
    color: white;
  }
}

.admin-btn-outline-primary {
  @extend .admin-btn;
  background-color: transparent;
  color: $blue;
  border-color: $blue;
  
  &:hover {
    background-color: $blue;
    color: white;
  }
  
  &:visited {
    color: $blue;
  }
}

.admin-btn-outline-info {
  @extend .admin-btn;
  background-color: transparent;
  color: #17a2b8;
  border-color: #17a2b8;
  
  &:hover {
    background-color: #17a2b8;
    color: white;
  }
}

.admin-btn-outline-warning {
  @extend .admin-btn;
  background-color: transparent;
  color: #ffc107;
  border-color: #ffc107;
  
  &:hover {
    background-color: #ffc107;
    color: #212529;
  }
}

.admin-btn-outline-danger {
  @extend .admin-btn;
  background-color: transparent;
  color: #dc3545;
  border-color: #dc3545;
  
  &:hover {
    background-color: #dc3545;
    color: white;
  }
}

.admin-btn-sm {
  padding: 4px 8px;
  font-size: 11px;
  line-height: 1.2;
}

.admin-btn-group {
  display: inline-flex;
  gap: 3px;
  flex-wrap: wrap;
  justify-content: center;
  
  .admin-btn {
    border-radius: 4px;
    white-space: nowrap;
  }
}

// Muted/Gray buttons for less prominent actions
.admin-btn-muted {
  @extend .admin-btn;
  background-color: #f3f4f6;
  color: #6b7280;
  border-color: #d1d5db;
  
  &:hover {
    background-color: #e5e7eb;
    color: #374151;
  }
}

.admin-btn-outline-muted {
  @extend .admin-btn;
  background-color: transparent;
  color: #6b7280;
  border-color: #d1d5db;
  
  &:hover {
    background-color: #f3f4f6;
    color: #374151;
  }
}

// Legacy button support
.btn {
  @extend .admin-btn;
}

.btn-primary {
  @extend .admin-btn-primary;
}

.btn-secondary {
  @extend .admin-btn-secondary;
}

.btn-sm {
  @extend .admin-btn-sm;
}

.btn-success {
  @extend .admin-btn;
  background-color: $green;
  color: white;
  border-color: $green;
  
  &:hover {
    background-color: darken($green, 10%);
    color: white;
  }
}

.btn-danger {
  @extend .admin-btn;
  background-color: #dc3545;
  color: white;
  border-color: #dc3545;
  
  &:hover {
    background-color: darken(#dc3545, 10%);
    color: white;
  }
}

.btn-outline-primary {
  @extend .admin-btn-outline-primary;
}

.btn-outline-info {
  @extend .admin-btn-outline-info;
}

.btn-outline-warning {
  @extend .admin-btn-outline-warning;
}

.btn-outline-danger {
  @extend .admin-btn-outline-danger;
}

.btn-group {
  @extend .admin-btn-group;
}

// Forms
.admin-form-group {
  margin-bottom: 16px;
  
  label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: $secondary-color;
  }
}

.admin-form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.4;
  
  &::placeholder {
    color: #9ca3af;
  }
  
  &:focus {
    outline: none;
    border-color: $blue;
    box-shadow: 0 0 0 3px rgba($blue, 0.1);
  }
}

.form-control {
  @extend .admin-form-control;
}

.form-group {
  @extend .admin-form-group;
}

// Grid System
.admin-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12px;
}

.admin-col {
  padding: 0 12px;
  
  &.admin-col-md-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  
  &.admin-col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }
  
  &.admin-col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  
  &.admin-col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

// Legacy Bootstrap grid support
.container-fluid {
  width: 100%;
  padding: 0 20px;
}

.row {
  @extend .admin-row;
}

.col-12 {
  @extend .admin-col;
  @extend .admin-col.admin-col-md-12;
}

.col-md-2 {
  @extend .admin-col;
  @extend .admin-col.admin-col-md-2;
  
  @media (max-width: $breakpoint-mobile) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-md-3 {
  @extend .admin-col;
  @extend .admin-col.admin-col-md-3;
  
  @media (max-width: $breakpoint-mobile) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-md-4 {
  @extend .admin-col;
  @extend .admin-col.admin-col-md-4;
  
  @media (max-width: $breakpoint-mobile) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

// Utilities
.admin-d-flex {
  display: flex;
}

.admin-justify-content-between {
  justify-content: space-between;
}

.admin-justify-content-center {
  justify-content: center;
}

.admin-align-items-center {
  align-items: center;
}

.admin-text-center {
  text-align: center;
}

.admin-text-muted {
  color: #6b7280;
}

.admin-text-danger {
  color: #dc3545;
}

.admin-mb-0 {
  margin-bottom: 0;
}

.admin-mb-4 {
  margin-bottom: 24px;
}

.admin-font-weight-bold {
  font-weight: bold;
}

// Legacy Bootstrap utilities
.d-flex {
  @extend .admin-d-flex;
}

.justify-content-between {
  @extend .admin-justify-content-between;
}

.justify-content-center {
  @extend .admin-justify-content-center;
}

.align-items-center {
  @extend .admin-align-items-center;
}

.text-center {
  @extend .admin-text-center;
}

.text-muted {
  @extend .admin-text-muted;
}

.text-danger {
  @extend .admin-text-danger;
}

.mb-0 {
  @extend .admin-mb-0;
}

.mb-4 {
  @extend .admin-mb-4;
}

.font-weight-bold {
  @extend .admin-font-weight-bold;
}

// Badge support
.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  display: inline-block;
  
  &.badge-success {
    background-color: #d1fae5;
    color: #065f46;
  }
  
  &.badge-secondary {
    background-color: #f3f4f6;
    color: #6b7280;
  }
}

// Alert styles
.alert {
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  
  &.alert-danger {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
    
    h6 {
      margin: 0 0 8px 0;
      font-weight: 600;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
    }
  }
}

// Card styles for Bootstrap compatibility
.card {
  @extend .admin-card;
}

.card-header {
  @extend .admin-card-header;
}

.card-body {
  @extend .admin-card-body;
}

.bg-light {
  background-color: #f8f9fa;
}

.bg-success {
  background-color: $green;
}

.bg-primary {
  background-color: $blue;
}

// Table styles
.table {
  @extend .admin-table;
}

.table-responsive {
  @extend .admin-table-responsive;
}

.table-striped {
  tr:nth-child(odd) {
    background-color: #f9fafb;
  }
}

.table-warning {
  @extend .admin-table-warning;
}

// Modal styles (basic support)
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: none;
  
  &.fade {
    // Add fade effect if needed
  }
}

.modal-dialog {
  position: relative;
  margin: 3rem auto;
  max-width: 500px;
  width: 90%;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  color: #6b7280;
  
  &:hover {
    color: #374151;
  }
}

// Form text
.form-text {
  font-size: 12px;
  margin-top: 4px;
  
  &.text-muted {
    color: #6b7280;
  }
}

// Results info text
.admin-results-info {
  margin: 10px 0;
  color: #6b7280;
  font-style: italic;
}

// Responsive adjustments
@media (max-width: $breakpoint-mobile) {
  .admin-container {
    margin: 10px;
  }
  
  .admin-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .admin-status-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .admin-filter-buttons {
    flex-wrap: wrap;
  }
  
  .admin-flex-table-header,
  .admin-flex-table-row {
    flex-direction: column;
  }
  
  .admin-flex-table-cell {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
    padding: 8px 12px;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .admin-btn-group {
    flex-direction: column;
    align-items: stretch;
  }
}