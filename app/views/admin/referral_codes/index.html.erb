<% content_for :title, "Referral Code Management" %>

<div class="admin-container">
  <div class="admin-header">
    <h1>Referral Code Management</h1>
    <%= link_to "Subscriptions", admin_subscriptions_path, class: "admin-btn-secondary" %>
  </div>

  <!-- Create New Code -->
  <div class="admin-card">
    <div class="admin-card-header">
      <h2>Create New Referral Code</h2>
    </div>
    <div class="admin-card-body">
          <%= form_with model: [:admin, @new_code], local: true do |form| %>
          <% if @new_code.errors.any? %>
            <div class="alert alert-danger">
              <h6>Please fix the following errors:</h6>
              <ul class="admin-mb-0">
                <% @new_code.errors.full_messages.each do |message| %>
                  <li><%= message %></li>
                <% end %>
              </ul>
            </div>
          <% end %>

          <div class="admin-row">
            <div class="admin-col admin-col-md-3">
              <div class="admin-form-group">
                <%= form.label :code, "Code" %>
                <%= form.text_field :code, class: "admin-form-control", placeholder: "e.g., WELCOME2024" %>
              </div>
            </div>
            <div class="admin-col admin-col-md-2">
              <div class="admin-form-group">
                <%= form.label :tier_upgrade_to, "Upgrade To" %>
                <%= form.select :tier_upgrade_to, 
                    options_for_select([
                      ['Premium', 'premium'],
                      ['Beta', 'beta']
                    ]), 
                    {}, 
                    { class: "admin-form-control" } %>
              </div>
            </div>
            <div class="admin-col admin-col-md-2">
              <div class="admin-form-group">
                <%= form.label :duration_months, "Duration (months)" %>
                <%= form.number_field :duration_months, class: "admin-form-control", value: 1, min: 1 %>
              </div>
            </div>
            <div class="admin-col admin-col-md-2">
              <div class="admin-form-group">
                <%= form.label :max_uses, "Max Uses" %>
                <%= form.number_field :max_uses, class: "admin-form-control", value: 1, min: 1 %>
              </div>
            </div>
            <div class="admin-col admin-col-md-3">
              <div class="admin-form-group">
                <%= form.label :expires_at, "Expires At" %>
                <%= form.datetime_local_field :expires_at, 
                    class: "admin-form-control",
                    value: 1.month.from_now.strftime("%Y-%m-%dT%H:%M") %>
              </div>
            </div>
          </div>
          <div class="admin-form-group">
            <%= form.label :description, "Description" %>
            <%= form.text_area :description, class: "admin-form-control", rows: 2, placeholder: "Optional description for this code" %>
          </div>
          <div class="admin-form-group">
            <%= form.submit "Create Code", class: "admin-btn-primary" %>
          </div>
          <% end %>
        </div>
      </div>

      <!-- Existing Codes -->
      <div class="admin-card">
        <div class="admin-card-header">
          <h5 class="admin-mb-0">Existing Referral Codes</h5>
        </div>
        <div class="admin-card-body">
          <div class="admin-table-responsive">
            <table class="admin-table table-striped">
              <thead>
                <tr>
                  <th>Code</th>
                  <th>Status</th>
                  <th>Upgrade To</th>
                  <th>Duration</th>
                  <th>Usage</th>
                  <th>Expires</th>
                  <th>Created By</th>
                  <th>Description</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <% @codes.each do |code| %>
                  <tr class="<%= 'admin-table-warning' if code.expires_at&.past? %>">
                    <td>
                      <code class="admin-font-weight-bold"><%= code.code %></code>
                    </td>
                    <td>
                      <span class="admin-status-badge admin-status-<%= code.status %>">
                        <%= code.status.humanize %>
                      </span>
                    </td>
                    <td>
                      <span class="admin-tier-badge admin-tier-<%= code.tier_upgrade_to %>">
                        <%= code.tier_upgrade_to.humanize %>
                      </span>
                    </td>
                    <td>
                      <%= pluralize(code.duration_months, 'month') %>
                    </td>
                    <td>
                      <span class="<%= 'admin-text-danger' if code.current_uses >= code.max_uses %>">
                        <%= code.current_uses %> / <%= code.max_uses %>
                      </span>
                    </td>
                    <td>
                      <% if code.expires_at %>
                        <%= code.expires_at.strftime("%b %d, %Y") %>
                        <% if code.expires_at.past? %>
                          <small class="admin-text-danger">(Expired)</small>
                        <% end %>
                      <% else %>
                        <span class="admin-text-muted">Never</span>
                      <% end %>
                    </td>
                    <td>
                      <%= code.created_by.email %>
                    </td>
                    <td>
                      <% if code.description.present? %>
                        <%= truncate(code.description, length: 50) %>
                      <% else %>
                        <span class="admin-text-muted">No description</span>
                      <% end %>
                    </td>
                    <td>
                      <div class="admin-btn-group" role="group">
                        <%= link_to "View", admin_referral_code_path(code), class: "admin-btn-outline-info admin-btn-sm" %>
                        <% if code.active? %>
                          <%= link_to "Disable", admin_referral_code_path(code), 
                              method: :patch, 
                              params: { referral_code: { status: 'disabled' } },
                              class: "admin-btn-outline-warning admin-btn-sm",
                              confirm: "Are you sure you want to disable this code?" %>
                        <% end %>
                        <%= link_to "Delete", admin_referral_code_path(code), 
                            method: :delete, 
                            class: "admin-btn-outline-danger admin-btn-sm",
                            confirm: "Are you sure you want to delete this code?" %>
                      </div>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <% if @pagy.pages > 1 %>
            <div class="admin-d-flex admin-justify-content-center">
              <%== pagy_nav(@pagy) %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>