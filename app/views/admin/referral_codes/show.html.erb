<% content_for :title, "Referral Code: #{@code.code}" %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Referral Code: <code><%= @code.code %></code></h1>
        <%= link_to "Back to Codes", admin_referral_codes_path, class: "btn btn-secondary" %>
      </div>

      <div class="row">
        <div class="col-md-8">
          <!-- Code Details -->
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">Code Details</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <strong>Code:</strong> <code class="font-weight-bold text-primary"><%= @code.code %></code><br>
                  <strong>Status:</strong> 
                  <span class="badge badge-<%= status_badge_class(@code.status) %>">
                    <%= @code.status.humanize %>
                  </span><br>
                  <strong>Upgrade To:</strong> 
                  <span class="badge badge-<%= tier_badge_class(@code.tier_upgrade_to) %>">
                    <%= @code.tier_upgrade_to.humanize %>
                  </span><br>
                  <strong>Duration:</strong> <%= pluralize(@code.duration_months, 'month') %><br>
                </div>
                <div class="col-md-6">
                  <strong>Usage:</strong> 
                  <span class="<%= 'text-danger' if @code.current_uses >= @code.max_uses %>">
                    <%= @code.current_uses %> / <%= @code.max_uses %>
                  </span><br>
                  <strong>Created:</strong> <%= @code.created_at.strftime("%B %d, %Y at %I:%M %p") %><br>
                  <strong>Created By:</strong> <%= @code.created_by.email %><br>
                  <strong>Expires:</strong> 
                  <% if @code.expires_at %>
                    <%= @code.expires_at.strftime("%B %d, %Y at %I:%M %p") %>
                    <% if @code.expires_at.past? %>
                      <span class="text-danger">(Expired)</span>
                    <% end %>
                  <% else %>
                    <span class="text-muted">Never</span>
                  <% end %><br>
                </div>
              </div>
              <% if @code.description.present? %>
                <hr>
                <strong>Description:</strong><br>
                <p class="mt-2"><%= simple_format(@code.description) %></p>
              <% end %>
            </div>
          </div>

          <!-- Update Code Form -->
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">Update Code</h5>
            </div>
            <div class="card-body">
              <%= form_with model: [:admin, @code], local: true do |form| %>
                <% if @code.errors.any? %>
                  <div class="alert alert-danger">
                    <h6>Please fix the following errors:</h6>
                    <ul class="mb-0">
                      <% @code.errors.full_messages.each do |message| %>
                        <li><%= message %></li>
                      <% end %>
                    </ul>
                  </div>
                <% end %>

                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <%= form.label :status, "Status" %>
                      <%= form.select :status, 
                          options_for_select([
                            ['Active', 'active'],
                            ['Expired', 'expired'],
                            ['Used Up', 'used_up'],
                            ['Disabled', 'disabled']
                          ], @code.status), 
                          {}, 
                          { class: "form-control" } %>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <%= form.label :expires_at, "Expires At" %>
                      <%= form.datetime_local_field :expires_at, 
                          value: @code.expires_at&.strftime("%Y-%m-%dT%H:%M"),
                          class: "form-control" %>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <%= form.label :description, "Description" %>
                  <%= form.text_area :description, class: "form-control", rows: 3 %>
                </div>
                <div class="form-group">
                  <%= form.submit "Update Code", class: "btn btn-primary" %>
                  <%= link_to "Delete Code", admin_referral_code_path(@code), 
                      method: :delete, 
                      class: "btn btn-danger",
                      confirm: "Are you sure you want to delete this code? This action cannot be undone." %>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <div class="col-md-4">
          <!-- Quick Stats -->
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">Quick Stats</h5>
            </div>
            <div class="card-body">
              <div class="text-center">
                <h3 class="text-primary"><%= @code.current_uses %></h3>
                <p class="mb-2">Times Used</p>
                
                <h3 class="text-info"><%= @code.max_uses - @code.current_uses %></h3>
                <p class="mb-2">Uses Remaining</p>
                
                <% if @code.expires_at %>
                  <% days_until_expiry = (@code.expires_at.to_date - Date.current).to_i %>
                  <h3 class="<%= days_until_expiry <= 7 ? 'text-warning' : 'text-success' %>">
                    <%= days_until_expiry %>
                  </h3>
                  <p class="mb-2">Days Until Expiry</p>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Usage Progress -->
          <div class="card mt-3">
            <div class="card-header">
              <h5 class="mb-0">Usage Progress</h5>
            </div>
            <div class="card-body">
              <% usage_percentage = (@code.current_uses.to_f / @code.max_uses * 100).round(1) %>
              <div class="progress mb-2">
                <div class="progress-bar <%= usage_percentage >= 100 ? 'bg-danger' : 'bg-primary' %>" 
                     style="width: <%= [usage_percentage, 100].min %>%">
                  <%= usage_percentage %>%
                </div>
              </div>
              <small class="text-muted">
                <%= @code.current_uses %> of <%= @code.max_uses %> uses
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>